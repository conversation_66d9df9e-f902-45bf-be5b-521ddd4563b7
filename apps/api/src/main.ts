import { NestFactory } from '@nestjs/core';
import express from 'express';
import { to<PERSON>odeHandler } from 'better-auth/node';
import { auth } from './utils/auth';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { bodyParser: false });
  app.enableCors({
    origin: true,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  });

  await app.listen(3000);
}

void bootstrap();
