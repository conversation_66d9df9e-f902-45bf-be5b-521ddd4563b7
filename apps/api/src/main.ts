import { NestFactory } from '@nestjs/core';
import express from 'express';
import { toNodeHandler } from 'better-auth/node';
import { auth } from './utils/auth';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, { bodyParser: false });

  const expressApp = express();
  expressApp.all('/api/auth/*', toNodeHandler(auth));
  app.use(expressApp);

  await app.listen(3000);
}

void bootstrap();
