import { Module } from '@nestjs/common';

import { LinksModule } from './links/links.module';

import { AppService } from './app.service';
import { AppController } from './app.controller';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
// import { AuthModule } from '@mguay/nestjs-better-auth';
import { UsersModule } from './users/users.module';
// import { auth } from './utils/auth';

@Module({
  imports: [
    LinksModule,
    UsersModule,
    ConfigModule.forRoot(),
    DatabaseModule,
    // AuthModule.forRoot(auth), // Temporarily disabled due to path-to-regexp issue
    UsersModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
