import { sql } from 'drizzle-orm';
import { pgTable, text, timestamp, index } from 'drizzle-orm/pg-core';
import { chatSessionType } from '../auth/enums';
import { users } from '../auth/users.schema';
import { resumeDocuments } from '../resume/documents.schema';
import { interviews } from '../interviews/interviews.schema';

export const chatSessions = pgTable(
  'chat_sessions',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => sql`gen_random_uuid()`),
    userId: text('user_id')
      .notNull()
      .references(() => users.id, { onDelete: 'cascade', onUpdate: 'cascade' }),
    resumeId: text('resume_id').references(() => resumeDocuments.id, {
      onDelete: 'set null',
      onUpdate: 'cascade',
    }),
    interviewId: text('interview_id').references(() => interviews.id, {
      onDelete: 'set null',
      onUpdate: 'cascade',
    }),
    sessionType: chatSessionType('session_type').notNull().default('GENERAL'),
    title: text('title'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp('updated_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (t) => ({
    userIdx: index('idx_chat_sessions_user').on(t.userId, t.createdAt),
    resumeIdx: index('idx_chat_sessions_resume').on(t.resumeId),
    interviewIdx: index('idx_chat_sessions_interview').on(t.interviewId),
  }),
);
