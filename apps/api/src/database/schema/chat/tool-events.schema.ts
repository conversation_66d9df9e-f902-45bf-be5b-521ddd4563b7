import { sql } from 'drizzle-orm';
import {
  pgTable,
  text,
  timestamp,
  jsonb,
  real,
  index,
} from 'drizzle-orm/pg-core';
import { toolCallType } from '../auth/enums';
import { chatMessages } from './messages.schema';

export const chatToolEvents = pgTable(
  'chat_tool_events',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => sql`gen_random_uuid()`),
    messageId: text('message_id')
      .notNull()
      .references(() => chatMessages.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    toolName: text('tool_name').notNull(),
    callType: toolCallType('call_type').notNull().default('OTHER'),
    request: jsonb('request')
      .notNull()
      .default(sql`'{}'::jsonb`),
    response: jsonb('response')
      .notNull()
      .default(sql`'{}'::jsonb`),
    latencyMs: real('latency_ms'),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (t) => ({
    messageIdx: index('idx_tool_events_message').on(t.messageId),
  }),
);
