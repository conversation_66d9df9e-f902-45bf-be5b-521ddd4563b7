import { pgTable, text, real, primaryKey } from 'drizzle-orm/pg-core';
import { chatMessages } from './messages.schema';
import { chatContextItems } from './context-items.schema';

export const chatMessageContext = pgTable(
  'chat_message_context',
  {
    messageId: text('message_id')
      .notNull()
      .references(() => chatMessages.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    contextItemId: text('context_item_id')
      .notNull()
      .references(() => chatContextItems.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    relevanceScore: real('relevance_score'),
  },
  (t) => ({
    pk: primaryKey({
      columns: [t.messageId, t.contextItemId],
      name: 'chat_message_context_pk',
    }),
  }),
);
