import { pgEnum } from "drizzle-orm/pg-core";

export const userRole = pgEnum("user_role", ["USER", "ADMIN"]);

export const resumeSectionType = pgEnum("resume_section_type", [
  "SUMMARY",
  "EX<PERSON>ERIENCE",
  "EDUCATION",
  "PROJECTS",
  "SK<PERSON><PERSON>",
  "OTHER",
]);
export const skillProficiency = pgEnum("skill_proficiency", [
  "NOVICE",
  "INTERMEDIATE",
  "ADVANCED",
  "EXPERT",
]);

export const interviewType = pgEnum("interview_type", [
  "CODING",
  "SYSTEM_DESIGN",
  "TECHNICAL_CONCEPTS",
  "B<PERSON><PERSON>VIOR<PERSON>",
  "CASE_STUDY",
  "DOMAIN_SPECIFIC",
]);
export const experienceLevel = pgEnum("experience_level", [
  "ENTRY",
  "JUNIOR",
  "MID",
  "SENIOR",
  "LEAD",
  "EXECUTIVE",
]);
export const difficultyLevel = pgEnum("difficulty_level", [
  "INTRODUCTORY",
  "BASI<PERSON>",
  "INTERME<PERSON>ATE",
  "ADVANCED",
  "EXPERT",
]);
export const interviewStyle = pgEnum("interview_style", [
  "FRIENDLY",
  "NEUTRAL",
  "CHALLENGING",
]);
export const interviewStatus = pgEnum("interview_status", [
  "PENDING",
  "IN_PROGRESS",
  "COMPLETED",
  "CANCELLED",
]);
export const meetingType = pgEnum("meeting_type", [
  "AI_PRACTICE",
  "LIVE_INTERVIEW",
  "PEER_PRACTICE",
]);
export const participantRole = pgEnum("participant_role", [
  "INTERVIEWER_AI",
  "CANDIDATE",
  "INTERVIEWER_HUMAN",
  "PEER",
]);
export const speakerRole = pgEnum("speaker_role", [
  "INTERVIEWER",
  "CANDIDATE",
  "SYSTEM",
]);
export const recordingStatus = pgEnum("recording_status", [
  "NOT_STARTED",
  "RECORDING",
  "PROCESSING",
  "COMPLETED",
  "FAILED",
]);

export const chatSessionType = pgEnum("chat_session_type", [
  "RESUME_REVIEW",
  "INTERVIEW_PREP",
  "INTERVIEW_RESULTS",
  "GENERAL",
]);
export const messageRole = pgEnum("message_role", [
  "USER",
  "ASSISTANT",
  "SYSTEM",
]);

export const mediaType = pgEnum("media_type", ["AUDIO", "VIDEO"]);

export const knowledgeSource = pgEnum("knowledge_source", [
  "RESUME",
  "LINKEDIN",
  "FILE",
  "NOTE",
  "URL",
  "EXTERNAL",
]);
export const toolCallType = pgEnum("tool_call_type", [
  "RETRIEVE",
  "FUNCTION_CALL",
  "WEB_SEARCH",
  "OTHER",
]);
