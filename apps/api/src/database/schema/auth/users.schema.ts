import { pgTable, text, timestamp, boolean } from 'drizzle-orm/pg-core';
import { userRole } from './enums';

export const users = pgTable('users', {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  bio: text('bio'),
  role: userRole('role').notNull().default('USER'),
  emailVerified: boolean('email_verified').notNull(),
  image: text('image'),
  createdAt: timestamp('created_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
  updatedAt: timestamp('updated_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
  stripeCustomerId: text('stripe_customer_id'),
  password: text('password'),
  username: text('username').notNull(),
  displayUsername: text('display_username'),
});
