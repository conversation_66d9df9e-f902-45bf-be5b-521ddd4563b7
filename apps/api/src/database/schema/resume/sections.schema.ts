import { sql } from 'drizzle-orm';
import { pgTable, text, integer, index } from 'drizzle-orm/pg-core';
import { resumeDocuments } from './documents.schema';
import { resumeSectionType } from '../auth/enums';

export const resumeSections = pgTable(
  'resume_sections',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => sql`gen_random_uuid()`),
    resumeId: text('resume_id')
      .notNull()
      .references(() => resumeDocuments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    sectionType: resumeSectionType('section_type').notNull(),
    content: text('content').notNull(),
    position: integer('position').notNull(),
  },
  (t) => ({
    resumePosIdx: index('idx_resume_sections_resume_pos').on(
      t.resumeId,
      t.position,
    ),
  }),
);
