import { sql } from 'drizzle-orm';
import {
  pgTable,
  text,
  timestamp,
  integer,
  jsonb,
  index,
} from 'drizzle-orm/pg-core';
import { knowledgeDocuments } from './documents.schema';

export const knowledgeChunks = pgTable(
  'knowledge_chunks',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => sql`gen_random_uuid()`),
    documentId: text('document_id')
      .notNull()
      .references(() => knowledgeDocuments.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    position: integer('position').notNull(),
    content: text('content').notNull(),
    hash: text('hash').notNull().unique(),
    tokenCount: integer('token_count'),
    externalVectorId: text('external_vector_id'),
    meta: jsonb('meta')
      .notNull()
      .default(sql`'{}'::jsonb`),
    createdAt: timestamp('created_at', { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (t) => ({
    documentPosIdx: index('idx_kchunks_document_pos').on(
      t.documentId,
      t.position,
    ),
    vectorIdIdx: index('idx_kchunks_vector_id').on(t.externalVectorId),
  }),
);
