import { pgTable, text, index, timestamp, jsonb } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { interviews } from './interviews.schema';
import { participantRole } from '../auth/enums';

export const interviewParticipants = pgTable(
  'interview_participants',
  {
    id: text('id')
      .primaryKey()
      .$defaultFn(() => sql`gen_random_uuid()`),

    interviewId: text('interview_id')
      .notNull()
      .references(() => interviews.id, {
        onDelete: 'cascade',
        onUpdate: 'cascade',
      }),
    role: participantRole('role').notNull(),
    identity: text('identity').notNull(),
    joinedAt: timestamp('joined_at', { withTimezone: true }),
    leftAt: timestamp('left_at', { withTimezone: true }),
    meta: jsonb('meta')
      .notNull()
      .default(sql`'{}'::jsonb`),
  },
  (t) => ({
    interviewIdx: index('idx_interview_participants_interview').on(
      t.interviewId,
    ),
  }),
);
