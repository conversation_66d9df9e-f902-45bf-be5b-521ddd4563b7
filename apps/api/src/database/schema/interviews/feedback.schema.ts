import { sql } from 'drizzle-orm';
import { pgTable, text, timestamp, real, jsonb } from 'drizzle-orm/pg-core';
import { interviews } from './interviews.schema';

export const interviewFeedback = pgTable('interview_feedback', {
  id: text('id')
    .primaryKey()
    .$defaultFn(() => sql`gen_random_uuid()`),
  interviewId: text('interview_id')
    .notNull()
    .references(() => interviews.id, {
      onDelete: 'cascade',
      onUpdate: 'cascade',
    })
    .unique(),
  overallScore: real('overall_score'),
  communicationScore: real('communication_score'),
  technicalDepthScore: real('technical_depth_score'),
  problemSolvingScore: real('problem_solving_score'),
  domainKnowledgeScore: real('domain_knowledge_score'),
  strengths: text('strengths')
    .array()
    .notNull()
    .default(sql`'{}'::text[]`),
  weaknesses: text('weaknesses')
    .array()
    .notNull()
    .default(sql`'{}'::text[]`),
  improvementAreas: text('improvement_areas')
    .array()
    .notNull()
    .default(sql`'{}'::text[]`),
  metrics: jsonb('metrics')
    .notNull()
    .default(sql`'{}'::jsonb`),
  recommendations: jsonb('recommendations')
    .notNull()
    .default(sql`'{}'::jsonb`),
  rubricVersion: text('rubric_version'),
  generatedAt: timestamp('generated_at', { withTimezone: true })
    .notNull()
    .defaultNow(),
});
